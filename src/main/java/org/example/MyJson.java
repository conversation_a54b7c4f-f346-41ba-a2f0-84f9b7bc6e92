package org.example;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.entity.Student;

import static org.example.jedisConnection.jedis;

public class MyJson {
    /**
     * 创建JSON对象
     */
    public static void creatJsonObject () {
        JSONObject  jsonObject= new JSONObject();
        jsonObject.put("name", "张三");
        jsonObject.put("age", 18);
        jsonObject.put("sex", "男");
        System.out.println(jsonObject);//JSON对象
        System.out.println(jsonObject.toJSONString());//JSON字符串
    }
    //将实体类转换成JSON对象
    public static void entityToJsonObject () {
        Student student = new Student("张三", 18, "男");
        JSONObject jsonObject=JSONObject.parseObject(JSON.toJSONString( student));
    }
    //保存
    public static void save () {
        Student student = new Student("张三", 18, "男");
        jedis.set("student", JSON.toJSONString( student));
    }



    public static void main(String[] args) {
        creatJsonObject();
        save();
        System.out.println("数据已保存到Redis");
    }
}
