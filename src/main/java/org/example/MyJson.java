package org.example;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.entity.Student;

import static org.example.jedisConnection.jedis;

public class MyJson {
    /**
     * 创建JSON对象
     */
    public static void creatJsonObject () {
        JSONObject  jsonObject= new JSONObject();
        jsonObject.put("name", "张三");
        jsonObject.put("age", 18);
        jsonObject.put("sex", "男");
        System.out.println(jsonObject);//JSON对象
        System.out.println(jsonObject.toJSONString());//JSON字符串
    }
    //将实体类转换成JSON对象
    public static void entityToJsonObject () {
        Student student = new Student("张三", 18, "男");
        JSONObject jsonObject=JSONObject.parseObject(JSON.toJSONString( student));
    }
    //保存
    public static void save () {
        if (jedis == null) {
            System.err.println("Redis连接未建立，无法保存数据");
            return;
        }

        try {
            Student student = new Student("张三", 18, "男");
            String jsonString = JSON.toJSONString(student);
            jedis.set("student", jsonString);
            System.out.println("保存的JSON数据: " + jsonString);
        } catch (Exception e) {
            System.err.println("保存数据到Redis时出错: " + e.getMessage());
            throw e;
        }
    }



    // 从Redis读取数据
    public static void load() {
        try {
            String savedData = jedis.get("student");
            if (savedData != null) {
                System.out.println("从Redis读取的数据: " + savedData);
                // 将JSON字符串转换回Student对象
                Student student = JSON.parseObject(savedData, Student.class);
                System.out.println("转换后的Student对象: " + student);
            } else {
                System.out.println("Redis中没有找到student数据");
            }
        } catch (Exception e) {
            System.err.println("从Redis读取数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        creatJsonObject();

        // 测试保存到Redis
        System.out.println("\n=== 测试保存到Redis ===");
        try {
            save();
            System.out.println("数据已保存到Redis");

            // 验证数据是否保存成功
            load();

            // 测试其他键值
            System.out.println("\n=== 测试其他Redis操作 ===");
            String fooValue = jedis.get("foo");
            System.out.println("foo的值: " + fooValue);

        } catch (Exception e) {
            System.err.println("Redis操作时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
