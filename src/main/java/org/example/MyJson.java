package org.example;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import org.entity.User;

import java.util.ArrayList;
import java.util.List;

import static org.example.jedisConnection.jedis;

public class MyJson {

    public static void objToJsonObject(){
        User user = new User("张三", 18, "男", "上海", "<EMAIL>");
        User user1 = new User("李四", 19, "女", "北京", "<EMAIL>");
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(user));
        JSONObject jsonObject1 = JSONObject.parseObject(JSONObject.toJSONString(user1));
        System.out.println(jsonObject);
        System.out.println(jsonObject1);
        jedis.set("user",jsonObject.toString());
        jedis.set("user1",jsonObject1.toString());
    }
    // 处理用户列表
    public static void userListOperation() {
        // 创建List<User>对象并添加3个对象
        List<User> userList = new ArrayList<>();
        userList.add(new User("张三", 18, "男", "上海", "<EMAIL>"));
        userList.add(new User("李四", 19, "女", "北京", "<EMAIL>"));
        userList.add(new User("王五", 25, "男", "广州", "<EMAIL>"));

        // 将集合转换为JSON字符串并存入Redis
        String userListJson = JSONObject.toJSONString(userList);
        jedis.set("userList", userListJson);

        // 从Redis中取出JSON字符串并转换为List<User>
        String jsonFromRedis = jedis.get("userList");
        List<User> parsedUserList = JSONObject.parseObject(jsonFromRedis, new TypeReference<List<User>>() {});

        // 输出结果
        System.out.println("从Redis中取出的用户列表:");
        for (User user : parsedUserList) {
            System.out.println(user);
        }
    }

    public static void main(String[] args) {
        objToJsonObject();
        userListOperation();

    }
}
