package org.example;

import com.alibaba.fastjson2.JSON;
import org.entity.Student;
import redis.clients.jedis.Jedis;

import static org.example.jedisConnection.jedis;

public class RedisTest {
    
    public static void testRedisOperations() {
        System.out.println("=== Redis功能测试 ===");
        
        if (jedis == null) {
            System.err.println("Redis连接未建立");
            return;
        }
        
        try {
            // 测试1: 基本字符串操作
            System.out.println("\n1. 测试基本字符串操作:");
            jedis.set("test_key", "test_value");
            String value = jedis.get("test_key");
            System.out.println("设置 test_key = test_value");
            System.out.println("读取 test_key = " + value);
            
            // 测试2: JSON对象存储
            System.out.println("\n2. 测试JSON对象存储:");
            Student student1 = new Student("李四", 20, "女");
            String jsonStr = JSON.toJSONString(student1);
            jedis.set("student1", jsonStr);
            System.out.println("保存学生对象: " + jsonStr);
            
            String retrievedJson = jedis.get("student1");
            Student retrievedStudent = JSON.parseObject(retrievedJson, Student.class);
            System.out.println("读取学生对象: " + retrievedStudent);
            
            // 测试3: 多个对象存储
            System.out.println("\n3. 测试多个对象存储:");
            Student student2 = new Student("王五", 22, "男");
            Student student3 = new Student("赵六", 19, "女");
            
            jedis.set("student2", JSON.toJSONString(student2));
            jedis.set("student3", JSON.toJSONString(student3));
            
            System.out.println("student2: " + jedis.get("student2"));
            System.out.println("student3: " + jedis.get("student3"));
            
            // 测试4: 检查所有学生相关的键
            System.out.println("\n4. 检查所有学生相关的键:");
            var keys = jedis.keys("student*");
            System.out.println("找到的学生键: " + keys);
            
            System.out.println("\n✅ 所有Redis操作测试通过！");
            
        } catch (Exception e) {
            System.err.println("❌ Redis操作失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void main(String[] args) {
        testRedisOperations();
    }
}
