package org.example;

import redis.clients.jedis.Jedis;

public class jedisConnection {
    static Jedis jedis;

    // 初始化Redis连接
    static {
        try {
            jedis = new Jedis("localhost", 6379);
            // 测试连接
            jedis.ping();
            System.out.println("Redis连接成功");
        } catch (Exception e) {
            System.err.println("Redis连接失败: " + e.getMessage());
            jedis = null;
        }
    }

    public static void main(String[] args) {
        if (jedis == null) {
            System.err.println("Redis连接未建立，程序退出");
            return;
        }

        try {
            jedis.set("foo", "bar");
            String value = jedis.get("foo");
            System.out.println("foo的值: " + value);
        } catch (Exception e) {
            System.err.println("Redis操作失败: " + e.getMessage());
        } finally {
            // 注意：在实际应用中，不应该在这里关闭连接，因为其他类可能还在使用
            // jedis.close();
        }
    }
}
