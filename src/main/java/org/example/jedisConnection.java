
package org.example;

import redis.clients.jedis.Jedis;

// Redis连接管理类
public class jedisConnection {
    // 【关键修复】：声明并初始化静态Jedis对象
    // static：静态变量，属于类而不是实例，可以被其他类通过类名直接访问
    // Jedis：Redis的Java客户端对象
    // new Jedis("localhost", 6379)：创建连接到本地Redis服务器的连接
    // localhost：Redis服务器地址（本机）
    // 6379：Redis默认端口号
    // 【之前的错误】：如果这里只声明不初始化（static Jedis jedis;），变量值为null
    static Jedis jedis = new Jedis("localhost", 6379);

    // 主方法，用于测试Redis连接
    public static void main(String[] args) {
        // 向Redis中设置一个键值对：key="foo", value="bar"
        jedis.set("foo", "bar");
        // 从Redis中获取key="foo"的值
        String value = jedis.get("foo");
        // 打印获取到的值，应该输出"bar"
        System.out.println(value);
    }
}
